2025-07-30 07:26:30.574 [main] INFO  c.example.backend.BackendApplication - Starting BackendApplication using Java 17.0.14 with PID 23548 (D:\projects\AIstrusys\backend\build\classes\java\main started by 32797 in D:\projects\AIstrusys\backend)
2025-07-30 07:26:30.581 [main] INFO  c.example.backend.BackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 07:26:32.150 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 07:26:32.380 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 220 ms. Found 31 JPA repository interfaces.
2025-07-30 07:26:33.057 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-07-30 07:26:33.074 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 07:26:33.075 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-30 07:26:33.178 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 07:26:33.178 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2552 ms
2025-07-30 07:26:33.941 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-30 07:26:33.981 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-AIstrusys - Starting...
2025-07-30 07:26:34.203 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-AIstrusys - Added connection com.mysql.cj.jdbc.ConnectionImpl@27d5eb00
2025-07-30 07:26:34.205 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-AIstrusys - Start completed.
2025-07-30 07:26:34.254 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-30 07:26:34.255 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-07-30 07:26:36.284 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 07:26:36.580 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-30 07:26:37.655 [main] INFO  c.e.backend.config.JacksonConfig - Jackson ObjectMapper配置完成
2025-07-30 07:26:38.159 [main] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-07-30 07:26:38.159 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-07-30 07:26:39.506 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 07:26:39.788 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-30 07:26:40.389 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 07:26:40.389 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 07:26:40.391 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 07:26:40.392 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path '/'
2025-07-30 07:26:40.406 [main] INFO  c.example.backend.BackendApplication - Started BackendApplication in 10.502 seconds (process running for 11.388)
2025-07-30 07:27:55.461 [http-nio-8081-exec-3] ERROR c.e.b.security.JwtTokenProvider - JWT token is expired: JWT expired at 2025-07-29T00:33:25Z. Current time: 2025-07-29T23:27:55Z, a difference of 82470460 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-30 07:27:55.472 [http-nio-8081-exec-3] ERROR c.e.b.s.JwtAuthenticationEntryPoint - 未经授权的访问: Full authentication is required to access this resource
2025-07-30 07:28:15.941 [http-nio-8081-exec-10] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 开始为知识点 26 选择练习题目，学生ID: 39
2025-07-30 07:28:15.957 [http-nio-8081-exec-10] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 26 使用SUBJECT配置: 5 道题
2025-07-30 07:28:15.957 [http-nio-8081-exec-10] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 26 可用题目数 2 不足基础配置 5，返回所有可用题目
2025-07-30 07:28:15.957 [http-nio-8081-exec-10] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 26 最终选择了 2 道练习题目
2025-07-30 07:28:15.957 [http-nio-8081-exec-10] INFO  c.e.b.s.impl.LearningServiceImpl - 知识点 26 的视频合集ID: 34
2025-07-30 07:28:18.723 [http-nio-8081-exec-3] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 开始为知识点 26 选择练习题目，学生ID: 39
2025-07-30 07:28:18.723 [http-nio-8081-exec-3] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 26 使用SUBJECT配置: 5 道题
2025-07-30 07:28:18.723 [http-nio-8081-exec-3] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 26 可用题目数 2 不足基础配置 5，返回所有可用题目
2025-07-30 07:28:18.723 [http-nio-8081-exec-3] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 26 最终选择了 2 道练习题目
2025-07-30 07:28:18.723 [http-nio-8081-exec-3] INFO  c.e.b.s.impl.LearningServiceImpl - 知识点 26 的视频合集ID: 34
2025-07-30 07:28:19.540 [http-nio-8081-exec-4] INFO  c.e.b.s.i.VideoCollectionServiceImpl - 获取视频合集详情，ID: 34
2025-07-30 07:28:19.540 [http-nio-8081-exec-4] INFO  c.e.b.s.i.VideoCollectionServiceImpl - 找到视频合集: 金属与金属化合物溶液的反应_20250627_230008
2025-07-30 07:28:19.555 [http-nio-8081-exec-4] INFO  c.e.b.s.i.VideoCollectionServiceImpl - 视频合集 金属与金属化合物溶液的反应_20250627_230008 包含 1 个视频
2025-07-30 07:28:26.510 [http-nio-8081-exec-3] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 开始为知识点 1 选择练习题目，学生ID: 39
2025-07-30 07:28:26.516 [http-nio-8081-exec-3] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 1 使用KNOWLEDGE_POINT配置: 5 道题
2025-07-30 07:28:26.517 [http-nio-8081-exec-3] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 1 可用题目数 4 不足基础配置 5，返回所有可用题目
2025-07-30 07:28:26.517 [http-nio-8081-exec-3] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 1 最终选择了 4 道练习题目
2025-07-30 07:28:26.517 [http-nio-8081-exec-3] INFO  c.e.b.s.impl.LearningServiceImpl - 知识点 1 的视频合集ID: 5
2025-07-30 07:28:27.372 [http-nio-8081-exec-4] INFO  c.e.b.s.i.VideoCollectionServiceImpl - 获取视频合集详情，ID: 5
2025-07-30 07:28:27.372 [http-nio-8081-exec-4] INFO  c.e.b.s.i.VideoCollectionServiceImpl - 找到视频合集: 化学变化和物理变化_20250624_143735
2025-07-30 07:28:27.386 [http-nio-8081-exec-4] INFO  c.e.b.s.i.VideoCollectionServiceImpl - 视频合集 化学变化和物理变化_20250624_143735 包含 2 个视频
2025-07-30 07:28:32.705 [http-nio-8081-exec-10] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 开始为知识点 1 选择练习题目，学生ID: 39
2025-07-30 07:28:32.705 [http-nio-8081-exec-10] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 1 使用KNOWLEDGE_POINT配置: 5 道题
2025-07-30 07:28:32.705 [http-nio-8081-exec-10] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 1 可用题目数 4 不足基础配置 5，返回所有可用题目
2025-07-30 07:28:32.705 [http-nio-8081-exec-10] INFO  c.e.b.s.i.QuestionSelectionServiceImpl - 知识点 1 最终选择了 4 道练习题目
2025-07-30 07:28:32.705 [http-nio-8081-exec-10] INFO  c.e.b.s.impl.LearningServiceImpl - 知识点 1 的视频合集ID: 5
2025-07-30 07:28:51.085 [http-nio-8081-exec-6] INFO  c.e.b.s.i.PracticeSessionServiceImpl - 保存练习结果成功 - 会话ID: 177, 学生ID: 39, 知识点ID: 1
2025-07-30 07:29:14.684 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 07:29:15.589 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-30 07:29:15.605 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 07:29:15.605 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-AIstrusys - Shutdown initiated...
2025-07-30 07:29:15.605 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-AIstrusys - Shutdown completed.
